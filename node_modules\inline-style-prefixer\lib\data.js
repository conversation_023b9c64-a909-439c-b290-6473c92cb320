'use strict';

Object.defineProperty(exports, "__esModule", {
  value: true
});

var _crossFade = require('inline-style-prefixer/lib/plugins/crossFade');

var _crossFade2 = _interopRequireDefault(_crossFade);

var _imageSet = require('inline-style-prefixer/lib/plugins/imageSet');

var _imageSet2 = _interopRequireDefault(_imageSet);

var _sizing = require('inline-style-prefixer/lib/plugins/sizing');

var _sizing2 = _interopRequireDefault(_sizing);

var _transition = require('inline-style-prefixer/lib/plugins/transition');

var _transition2 = _interopRequireDefault(_transition);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var w = ['Webkit'];
var m = ['Moz'];
var ms = ['ms'];
var wm = ['Webkit', 'Moz'];
var wms = ['Webkit', 'ms'];
var wmms = ['Webkit', 'Moz', 'ms'];

exports.default = {
  plugins: [_crossFade2.default, _imageSet2.default, _sizing2.default, _transition2.default],
  prefixMap: {
    textEmphasisPosition: w,
    textEmphasis: w,
    textEmphasisStyle: w,
    textEmphasisColor: w,
    boxDecorationBreak: w,
    maskImage: w,
    maskMode: w,
    maskRepeat: w,
    maskPosition: w,
    maskClip: w,
    maskOrigin: w,
    maskSize: w,
    maskComposite: w,
    mask: w,
    maskBorderSource: w,
    maskBorderMode: w,
    maskBorderSlice: w,
    maskBorderWidth: w,
    maskBorderOutset: w,
    maskBorderRepeat: w,
    maskBorder: w,
    maskType: w,
    appearance: w,
    userSelect: w,
    backdropFilter: w,
    clipPath: w,
    hyphens: w,
    textOrientation: w,
    tabSize: m,
    fontKerning: w,
    textSizeAdjust: w,
    textDecorationStyle: w,
    textDecorationSkip: w,
    textDecorationLine: w,
    textDecorationColor: w
  }
};