/**
 * Copyright (c) 650 Industries.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
import type { LogLevel } from '../Data/LogBoxLog';
type Props = {
    onSelectIndex: (selectedIndex: number) => void;
    level: LogLevel;
};
export declare function LogBoxInspectorHeader(props: Props): React.JSX.Element;
export {};
//# sourceMappingURL=LogBoxInspectorHeader.d.ts.map